/*! This file is auto-generated */
!function(t){var a,i=t("#choose-from-library-button"),s=t("#site-icon-preview"),r=t("#browser-icon-preview"),n=t("#app-icon-preview"),l=t("#site_icon_hidden_field"),o=t("#js-remove-site-icon");function c(t){var e=t.get("width"),t=t.get("height"),a=512,i=512,s=a/i,r=a,n=i;return s<e/t?a=(i=t)*s:i=(a=e)/s,{aspectRatio:a+":"+i,handles:!0,keys:!0,instance:!0,persistent:!0,imageWidth:e,imageHeight:t,minWidth:a<r?a:r,minHeight:i<n?i:n,x1:s=(e-a)/2,y1:r=(t-i)/2,x2:a+s,y2:i+r}}function d(t){var e,a=t.alt?(e=wp.i18n.sprintf(wp.i18n.__("App icon preview: Current image: %s"),t.alt),wp.i18n.sprintf(wp.i18n.__("Browser icon preview: Current image: %s"),t.alt)):(e=wp.i18n.sprintf(wp.i18n.__("App icon preview: The current image has no alternative text. The file name is: %s"),t.filename),wp.i18n.sprintf(wp.i18n.__("Browser icon preview: The current image has no alternative text. The file name is: %s"),t.filename));n.attr({src:t.url,alt:e}),r.attr({src:t.url,alt:a}),s.removeClass("hidden"),o.removeClass("hidden"),document.documentElement.style.setProperty("--site-icon-url","url("+t.url+")"),"1"!==i.attr("data-state")&&i.attr({class:i.attr("data-alt-classes"),"data-alt-classes":i.attr("class"),"data-state":"1"}),i.text(i.attr("data-update-text"))}i.on("click",function(){var e=t(this);(a=wp.media({button:{text:e.data("update"),close:!1},states:[new wp.media.controller.Library({title:e.data("choose-text"),library:wp.media.query({type:"image"}),date:!1,suggestedWidth:e.data("size"),suggestedHeight:e.data("size")}),new wp.media.controller.SiteIconCropper({control:{params:{width:e.data("size"),height:e.data("size")}},imgSelectOptions:c})]})).on("cropped",function(t){l.val(t.id),d(t),a.close(),a=null}),a.on("select",function(){var t=a.state().get("selection").first();t.attributes.height===e.data("size")&&e.data("size")===t.attributes.width?(d(t.attributes),a.close(),l.val(t.id)):a.setState("cropper")}),a.open()}),o.on("click",function(){l.val("false"),t(this).toggleClass("hidden"),s.toggleClass("hidden"),r.attr({src:"",alt:""}),n.attr({src:"",alt:""}),i.attr({class:i.attr("data-alt-classes"),"data-alt-classes":i.attr("class"),"data-state":""}).text(i.attr("data-choose-text")).trigger("focus")})}(jQuery);