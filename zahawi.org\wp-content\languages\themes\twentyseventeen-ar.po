# Translation of Themes - Twenty Seventeen in Arabic
# This file is distributed under the same license as the Themes - Twenty Seventeen package.
msgid ""
msgstr ""
"PO-Revision-Date: 2019-04-26 19:47:09+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=6; plural=n==0 ? 0 : n==1 ? 1 : n==2 ? 2 : n%100>=3 && n%100<=10 ? 3 : n%100>=11 && n%100<=99 ? 4 : 5;\n"
"X-Generator: GlotPress/2.4.0-alpha\n"
"Language: ar\n"
"Project-Id-Version: Themes - Twenty Seventeen\n"

#. Description of the theme
msgid "Twenty Seventeen brings your site to life with header video and immersive featured images. With a focus on business sites, it features multiple sections on the front page as well as widgets, navigation and social menus, a logo, and more. Personalize its asymmetrical grid with a custom color scheme and showcase your multimedia content with post formats. Our default theme for 2017 works great in many languages, for any abilities, and on any device."
msgstr "قالب Twenty Seventeen يضفي الحيوية على موقعك من خلال فيديوهات رأسية وصور مميزة غامرة. من خلال التركيز على مواقع الأعمال، فهي تتميز بأقسام متعددة في الصفحة الأولى بالإضافة إلى عناصر ودجات، والتصفح والقوائم الاجتماعية وشعار والمزيد. قم بإضفاء الطابع الشخصي وأعرض أعمالك باستخدام نظام ألوان مخصص وعرض محتوى الوسائط المتعددة الخاص بك بتنسيقات المقال. يعمل القالب لعام 2017 بشكل رائع مع العديد من اللغات، ولأي قدرات ، وعلى أي جهاز."

#. Theme Name of the theme
msgid "Twenty Seventeen"
msgstr "Twenty Seventeen"

#: template-parts/footer/footer-widgets.php:18
msgid "Footer"
msgstr "تذييل"

#: functions.php:338
msgid "Add widgets here to appear in your sidebar on blog posts and archive pages."
msgstr "إضافة ودجات هنا لتظهر في الشريط الجانبي في مقالات المدونة وصفحات الأرشيف."

#: sidebar.php:18 functions.php:336
msgid "Blog Sidebar"
msgstr "الشريط الجانبي للمدونة"

#: template-parts/navigation/navigation-top.php:31
#: template-parts/header/site-branding.php:34
msgid "Scroll down to content"
msgstr "الانتقال للأسفل إلى المحتوى"

#: functions.php:179
msgctxt "Theme starter content"
msgid "Coffee"
msgstr "قهوة"

#: functions.php:175
msgctxt "Theme starter content"
msgid "Sandwich"
msgstr "شطيرة"

#: functions.php:171
msgctxt "Theme starter content"
msgid "Espresso"
msgstr "اسبريسو"

#: inc/custom-header.php:128
msgid "Pause background video"
msgstr "إيقاف مؤقت لفيديو الخلفية"

#: inc/custom-header.php:127
msgid "Play background video"
msgstr "تشغيل فيديو الخلفية"

#: inc/template-tags.php:151
msgid "Front Page Section %1$s Placeholder"
msgstr "قسم الواجهة الأمامية %1$s تسمية نائبة"

#: inc/customizer.php:109
msgid "When the two-column layout is assigned, the page title is in one column and content is in the other."
msgstr "عند تعيين التصميم ذي العمودين، يكون عنوان الصفحة في عمود واحد ويكون المحتوى في العمود الآخر."

#: single.php:34
msgid "Next Post"
msgstr "المقالة التالية"

#: index.php:27
msgid "Posts"
msgstr "المقالات"

#: inc/template-tags.php:89
msgid "Tags"
msgstr "الوسوم"

#: inc/template-tags.php:85
msgid "Categories"
msgstr "التصنيفات"

#. translators: used between list items, there is a space after the comma
#: inc/template-tags.php:66
msgid ", "
msgstr "،"

#. translators: %s: post date
#: inc/template-tags.php:52
msgid "<span class=\"screen-reader-text\">Posted on</span> %s"
msgstr "<span class=\"screen-reader-text\">نُشر في</span> %s"

#. translators: %s: post author
#: inc/template-tags.php:21
msgid "by %s"
msgstr "بواسطة %s"

#: inc/icon-functions.php:44
msgid "Please define an SVG icon filename."
msgstr "يُرجى تحديد اسم ملف أيقونة SVG."

#: inc/icon-functions.php:39
msgid "Please define default parameters in the form of an array."
msgstr "يُرجى تحديد المعلمات الافتراضية في شكل مصفوفة."

#: inc/customizer.php:143
msgid "Select pages to feature in each area from the dropdowns. Add an image to a section by setting a featured image in the page editor. Empty sections will not be displayed."
msgstr "حدد الصفحات المراد عرضها في كل منطقة من القائمة المنسدلة. أضف صورة إلى قسم عن طريق تعيين صورة مميزة في محرر الصفحة. لن يتم عرض الأقسام الفارغة."

#. translators: %d is the front page section number
#: inc/customizer.php:142
msgid "Front Page Section %d Content"
msgstr "قسم الواجهة الأمامية %d محتوى"

#: inc/customizer.php:112 inc/customizer.php:171
msgid "Two Column"
msgstr "عمودين"

#: inc/customizer.php:111 inc/customizer.php:170
msgid "One Column"
msgstr "عمود واحد"

#: inc/customizer.php:106
msgid "Page Layout"
msgstr "تخطيط الصفحة"

#: inc/customizer.php:89
msgid "Theme Options"
msgstr "خيارات القالب"

#: inc/customizer.php:64
msgid "Custom"
msgstr "مُخصص"

#: inc/customizer.php:62
msgid "Light"
msgstr "فاتخ"

#: inc/customizer.php:60
msgid "Color Scheme"
msgstr "نظام الألوان"

#: inc/custom-header.php:56
msgid "Default Header Image"
msgstr "صورة الترويسة الإفتراضية"

#: functions.php:360
msgid "Footer 2"
msgstr "تذييل 2"

#: functions.php:350 functions.php:362
msgid "Add widgets here to appear in your footer."
msgstr "أضف بعض الودجات هنا لتظهر في تذييل موقعك."

#: functions.php:348
msgid "Footer 1"
msgstr "تذييل 1"

#: functions.php:289
msgctxt "Libre Franklin font: on or off"
msgid "on"
msgstr "on"

#: template-parts/navigation/navigation-top.php:12 functions.php:64
#: functions.php:203
msgid "Top Menu"
msgstr "القائمة العلوية"

#: comments.php:62
msgid "Reply"
msgstr "رد"

#: template-parts/post/content-none.php:28
msgid "It seems we can&rsquo;t find what you&rsquo;re looking for. Perhaps searching can help."
msgstr "يبدو أننا لم نعثر على ما تطلب. لعل استعمال البحث قد يساعد."

#: search.php:54
msgid "Sorry, but nothing matched your search terms. Please try again with some different keywords."
msgstr "عذرا، لا يوجد شيء يتطابق مع كلمات البحث التي استعملتها، المرجو المحاولة من جديد باستعمال كلمات مفتاحية أخرى."

#: template-parts/post/content-none.php:24
msgid "Ready to publish your first post? <a href=\"%1$s\">Get started here</a>."
msgstr "هل أنت على استعداد لنشر مقالتك الأولى؟ <a href=\"%1$s\">ابدأ من هنا</a>."

#: template-parts/post/content-none.php:17 search.php:21
msgid "Nothing Found"
msgstr "لم يتم العثور على نتائج"

#: single.php:33
msgid "Previous Post"
msgstr "المقالة السابقة"

#: single.php:33 comments.php:71
msgid "Previous"
msgstr "السابق"

#: single.php:34 comments.php:72
msgid "Next"
msgstr "التالي"

#: searchform.php:20
msgctxt "submit button"
msgid "Search"
msgstr "بحث"

#: searchform.php:19
msgctxt "placeholder"
msgid "Search &hellip;"
msgstr "بحث &hellip;"

#: searchform.php:17
msgctxt "label"
msgid "Search for:"
msgstr "البحث عن:"

#: search.php:19
msgid "Search Results for: %s"
msgstr "نتائج البحث عن: %s"

#. translators: %s: Name of current post
#: template-parts/page/content-front-page-panels.php:45
#: template-parts/page/content-front-page.php:42
#: template-parts/post/content-audio.php:84 template-parts/post/content.php:57
#: template-parts/post/content-gallery.php:71
#: template-parts/post/content-video.php:83
#: template-parts/post/content-image.php:61 functions.php:390
msgid "Continue reading<span class=\"screen-reader-text\"> \"%s\"</span>"
msgstr "متابعة قراءة<span class=\"screen-reader-text\"> \"%s\"</span>"

#: inc/customizer.php:63
msgid "Dark"
msgstr "داكن"

#: inc/back-compat.php:39 inc/back-compat.php:52 inc/back-compat.php:70
msgid "Twenty Seventeen requires at least WordPress version 4.7. You are running version %s. Please upgrade and try again."
msgstr "Twenty Seventeen يتطلب نسخة الووردبريس 4.7 على الأقل. أنت تستخدم النسخة %s. يرجى الترقية والمحاولة مرة أخرى."

#. translators: %s: Name of current post
#: inc/template-tags.php:117
msgid "Edit<span class=\"screen-reader-text\"> \"%s\"</span>"
msgstr "تحرير<span class=\"screen-reader-text\"> \"%s\"</span>"

#: template-parts/page/content-page.php:26
#: template-parts/post/content-audio.php:91 template-parts/post/content.php:64
#: template-parts/post/content-gallery.php:78
#: template-parts/post/content-video.php:90
#: template-parts/post/content-image.php:68
msgid "Pages:"
msgstr "الصفحات:"

#: template-parts/navigation/navigation-top.php:17
msgid "Menu"
msgstr "القائمة"

#: header.php:27
msgid "Skip to content"
msgstr "التجاوز إلى المحتوى"

#: functions.php:482
msgid "Collapse child menu"
msgstr "طي القائمة الفرعية"

#: functions.php:481
msgid "Expand child menu"
msgstr "توسيع القائمة الفرعية"

#: functions.php:65 functions.php:214
msgid "Social Links Menu"
msgstr "قائمة الروابط الإجتماعية"

#: template-parts/footer/site-info.php:19
msgid "Proudly powered by %s"
msgstr "مدعوم بواسطة %s"

#: footer.php:26
msgid "Footer Social Links Menu"
msgstr "قائمة الروابط الإجتماعية في التذييل"

#: comments.php:82
msgid "Comments are closed."
msgstr "التعليقات مغلقة."

#. translators: 1: number of comments, 2: post title
#: comments.php:41
msgctxt "comments title"
msgid "%1$s Reply to &ldquo;%2$s&rdquo;"
msgid_plural "%1$s Replies to &ldquo;%2$s&rdquo;"
msgstr[0] "لا توجد ردود على &ldquo;%2$s&rdquo;"
msgstr[1] "ردّ واحد على &ldquo;%2$s&rdquo;"
msgstr[2] "ردّين على &ldquo;%2$s&rdquo;"
msgstr[3] "%1$s ردود على &ldquo;%2$s&rdquo;"
msgstr[4] "%1$s ردّ على &ldquo;%2$s&rdquo;"
msgstr[5] "%1$s ردّ على &ldquo;%2$s&rdquo;"

#. translators: %s: post title
#: comments.php:37
msgctxt "comments title"
msgid "One Reply to &ldquo;%s&rdquo;"
msgstr "ردّ واحد على &ldquo;%s&rdquo;"

#: archive.php:50 search.php:47 index.php:54
msgid "Page"
msgstr "الصفحة"

#: 404.php:21
msgid "Oops! That page can&rsquo;t be found."
msgstr "عذراً! لايمكن العثور على تلك الصفحة."

#: 404.php:24
msgid "It looks like nothing was found at this location. Maybe try a search?"
msgstr "يبدو أنه لم يتم إيجاد شيء في هذا المكان. ربما تريد البحث؟"

#: archive.php:48 search.php:45 index.php:52
msgid "Previous page"
msgstr "الصفحة السابقة"

#: archive.php:49 search.php:46 index.php:53
msgid "Next page"
msgstr "الصفحة التالية"

#. Author of the theme
msgid "the WordPress team"
msgstr "فريق ووردبريس"

#. Theme URI of the theme
msgid "https://wordpress.org/themes/twentyseventeen/"
msgstr "https://wordpress.org/themes/twentyseventeen/"

#. Author URI of the theme
#: template-parts/footer/site-info.php:18
msgid "https://wordpress.org/"
msgstr "https://wordpress.org/"