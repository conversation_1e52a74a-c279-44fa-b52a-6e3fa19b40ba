/*! This file is auto-generated */
!function(o){var s,a,t,n,i,r,l,p,d,c,u,h,f,w=!1,m=!1,v=wp.i18n.__;function g(){"function"!=typeof zxcvbn?setTimeout(g,50):(!a.val()||h.hasClass("is-open")?(a.val(a.data("pw")),a.trigger("pwupdate")):C(),x(),y(),1!==parseInt(r.data("start-masked"),10)?a.attr("type","text"):r.trigger("click"),o("#pw-weak-text-label").text(v("Confirm use of weak password")),"mailserver_pass"!==a.prop("id")&&o(a).trigger("focus"))}function b(e){r.attr({"aria-label":v(e?"Show password":"Hide password")}).find(".text").text(v(e?"Show":"Hide")).end().find(".dashicons").removeClass(e?"dashicons-hidden":"dashicons-visibility").addClass(e?"dashicons-visibility":"dashicons-hidden")}function y(){r||(r=s.find(".wp-hide-pw")).show().on("click",function(){"password"===a.attr("type")?(a.attr("type","text"),b(!1)):(a.attr("type","password"),b(!0))})}function k(e,s,a){var t=o("<div />",{role:"alert"});t.addClass("notice inline"),t.addClass("notice-"+(s?"success":"error")),t.text(o(o.parseHTML(a)).text()).wrapInner("<p />"),e.prop("disabled",s),e.siblings(".notice").remove(),e.before(t)}function _(){var e;s=o(".user-pass1-wrap, .user-pass-wrap, .mailserver-pass-wrap, .reset-pass-submit"),o(".user-pass2-wrap").hide(),p=o("#submit, #wp-submit").on("click",function(){w=!1}),l=p.add(" #createusersub"),n=o(".pw-weak"),(i=n.find(".pw-checkbox")).on("change",function(){l.prop("disabled",!i.prop("checked"))}),(a=o("#pass1, #mailserver_pass")).length?(d=a.val(),1===parseInt(a.data("reveal"),10)&&g(),a.on("input pwupdate",function(){a.val()!==d&&(d=a.val(),a.removeClass("short bad good strong"),x())})):a=o("#user_pass"),t=o("#pass2").on("input",function(){0<t.val().length&&(a.val(t.val()),t.val(""),d="",a.trigger("pwupdate"))}),a.is(":hidden")&&(a.prop("disabled",!0),t.prop("disabled",!0)),h=s.find(".wp-pwd"),e=s.find("button.wp-generate-pw"),y(),e.show(),e.on("click",function(){w=!0,e.not(".skip-aria-expanded").attr("aria-expanded","true"),h.show().addClass("is-open"),a.attr("disabled",!1),t.attr("disabled",!1),g(),b(!1),wp.ajax.post("generate-password").done(function(e){a.data("pw",e)})}),s.find("button.wp-cancel-pw").on("click",function(){w=!1,a.prop("disabled",!0),t.prop("disabled",!0),a.val("").trigger("pwupdate"),b(!1),h.hide().removeClass("is-open"),l.prop("disabled",!1),e.attr("aria-expanded","false")}),s.closest("form").on("submit",function(){w=!1,a.prop("disabled",!1),t.prop("disabled",!1),t.val(a.val())})}function C(){var e=o("#pass1").val();if(o("#pass-strength-result").removeClass("short bad good strong empty"),e&&""!==e.trim())switch(wp.passwordStrength.meter(e,wp.passwordStrength.userInputDisallowedList(),e)){case-1:o("#pass-strength-result").addClass("bad").html(pwsL10n.unknown);break;case 2:o("#pass-strength-result").addClass("bad").html(pwsL10n.bad);break;case 3:o("#pass-strength-result").addClass("good").html(pwsL10n.good);break;case 4:o("#pass-strength-result").addClass("strong").html(pwsL10n.strong);break;case 5:o("#pass-strength-result").addClass("short").html(pwsL10n.mismatch);break;default:o("#pass-strength-result").addClass("short").html(pwsL10n.short)}else o("#pass-strength-result").addClass("empty").html("&nbsp;")}function x(){var e=o("#pass-strength-result");e.length&&(e=e[0]).className&&(a.addClass(e.className),o(e).is(".short, .bad")?(i.prop("checked")||l.prop("disabled",!0),n.show()):(o(e).is(".empty")?(l.prop("disabled",!0),i.prop("checked",!1)):l.prop("disabled",!1),n.hide()))}new ClipboardJS(".application-password-display .copy-button").on("success",function(e){var s=o(e.trigger),a=o(".success",s.closest(".application-password-display"));e.clearSelection(),clearTimeout(f),a.removeClass("hidden"),f=setTimeout(function(){a.addClass("hidden")},3e3),wp.a11y.speak(v("Application password has been copied to your clipboard."))}),o(function(){var e,a,t,n,i=o("#display_name"),s=i.val(),r=o("#wp-admin-bar-my-account").find(".display-name");o("#pass1").val("").on("input pwupdate",C),o("#pass-strength-result").show(),o(".color-palette").on("click",function(){o(this).siblings('input[name="admin_color"]').prop("checked",!0)}),i.length&&(o("#first_name, #last_name, #nickname").on("blur.user_profile",function(){var a=[],t={display_nickname:o("#nickname").val()||"",display_username:o("#user_login").val()||"",display_firstname:o("#first_name").val()||"",display_lastname:o("#last_name").val()||""};t.display_firstname&&t.display_lastname&&(t.display_firstlast=t.display_firstname+" "+t.display_lastname,t.display_lastfirst=t.display_lastname+" "+t.display_firstname),o.each(o("option",i),function(e,s){a.push(s.value)}),o.each(t,function(e,s){s&&(s=s.replace(/<\/?[a-z][^>]*>/gi,""),t[e].length)&&-1===o.inArray(s,a)&&(a.push(s),o("<option />",{text:s}).appendTo(i))})}),i.on("change",function(){var e;t===n&&(e=this.value.trim()||s,r.text(e))})),e=o("#color-picker"),a=o("#colors-css"),t=o("input#user_id").val(),n=o('input[name="checkuser_id"]').val(),e.on("click.colorpicker",".color-option",function(){var e,s=o(this);if(!s.hasClass("selected")&&(s.siblings(".selected").removeClass("selected"),s.addClass("selected").find('input[type="radio"]').prop("checked",!0),t===n)){if((a=0===a.length?o('<link rel="stylesheet" />').appendTo("head"):a).attr("href",s.children(".css_url").val()),"undefined"!=typeof wp&&wp.svgPainter){try{e=JSON.parse(s.children(".icon_colors").val())}catch(e){}e&&(wp.svgPainter.setColors(e),wp.svgPainter.paint())}o.post(ajaxurl,{action:"save-user-color-scheme",color_scheme:s.children('input[name="admin_color"]').val(),nonce:o("#color-nonce").val()}).done(function(e){e.success&&o("body").removeClass(e.data.previousScheme).addClass(e.data.currentScheme)})}}),_(),o("#generate-reset-link").on("click",function(){var s=o(this),e={user_id:userProfileL10n.user_id,nonce:userProfileL10n.nonce},e=(s.parent().find(".notice-error").remove(),wp.ajax.post("send-password-reset",e));e.done(function(e){k(s,!0,e)}),e.fail(function(e){k(s,!1,e)})}),l.on("click",function(){m=!0}),c=o("#your-profile, #createuser"),u=c.serialize()}),o("#destroy-sessions").on("click",function(e){var s=o(this);wp.ajax.post("destroy-sessions",{nonce:o("#_wpnonce").val(),user_id:o("#user_id").val()}).done(function(e){s.prop("disabled",!0),s.siblings(".notice").remove(),s.before('<div class="notice notice-success inline" role="alert"><p>'+e.message+"</p></div>")}).fail(function(e){s.siblings(".notice").remove(),s.before('<div class="notice notice-error inline" role="alert"><p>'+e.message+"</p></div>")}),e.preventDefault()}),window.generatePassword=g,o(window).on("beforeunload",function(){return!0===w?v("Your new password has not been saved."):u===c.serialize()||m?void 0:v("The changes you made will be lost if you navigate away from this page.")}),o(function(){o(".reset-pass-submit").length&&o(".reset-pass-submit button.wp-generate-pw").trigger("click")})}(jQuery);